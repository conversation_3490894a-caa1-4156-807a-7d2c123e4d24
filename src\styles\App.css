* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.converter-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.editor-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e0e0e0;
}

.section-header h2 {
  font-size: 1.5rem;
  color: #333;
  font-weight: 600;
}

.button-group {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.btn-secondary:hover:not(:disabled) {
  background: #e9ecef;
  transform: translateY(-1px);
}

.json-input,
.yaml-output,
.yaml-editor {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.natural-language-preview {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-content {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 1rem;
  background: #fafafa;
}

.natural-language-text {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 0.9rem;
  line-height: 1.6;
  white-space: pre-wrap;
  color: #333;
}

.placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  color: #666;
}

.placeholder p {
  margin-bottom: 0.5rem;
}

.hint {
  font-size: 0.85rem;
  font-style: italic;
  max-width: 300px;
}

/* Adaptive YAML Editor Styles */
.adaptive-controls {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #667eea;
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.toggle-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
}

.toggle-control input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
}

.toggle-label {
  color: #495057;
}

/* Conversion Mode Controls */
.conversion-mode-controls {
  margin-bottom: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #e8f4fd 0%, #d1ecf1 100%);
  border-radius: 6px;
  border-left: 4px solid #17a2b8;
}

.conversion-mode-controls h4 {
  margin: 0 0 0.75rem 0;
  color: #0c5460;
  font-size: 1rem;
}

.mode-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.mode-option {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 200px;
}

.mode-option:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: #17a2b8;
}

.mode-option input[type="radio"] {
  margin-top: 0.2rem;
  accent-color: #17a2b8;
}

.mode-label {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.mode-label small {
  color: #6c757d;
  font-size: 0.8rem;
  font-weight: normal;
}

/* Moveworks Validation Styles */
.moveworks-validation {
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid;
}

.moveworks-validation.valid {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border-left-color: #28a745;
}

.moveworks-validation.invalid {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border-left-color: #dc3545;
}

.moveworks-validation h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
}

.moveworks-validation.valid h4 {
  color: #155724;
}

.moveworks-validation.invalid h4 {
  color: #721c24;
}

.validation-status {
  margin-bottom: 0.75rem;
}

.status-valid {
  color: #155724;
  font-weight: 600;
  font-size: 0.9rem;
}

.status-invalid {
  color: #721c24;
  font-weight: 600;
  font-size: 0.9rem;
}

.validation-issues h5 {
  margin: 0 0 0.5rem 0;
  color: #721c24;
  font-size: 0.9rem;
}

.validation-issues ul {
  margin: 0;
  padding-left: 1.5rem;
}

.validation-issues li {
  color: #721c24;
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.suggestions-panel {
  margin-bottom: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: 6px;
  border-left: 4px solid #f39c12;
}

.suggestions-panel h4 {
  margin: 0 0 0.75rem 0;
  color: #856404;
  font-size: 1rem;
}

.formatting-suggestions {
  margin-bottom: 0.75rem;
}

.formatting-suggestions pre {
  background: rgba(255, 255, 255, 0.7);
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #856404;
  margin: 0;
}

.context-suggestions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background: rgba(255, 255, 255, 0.95);
}

.suggestion-text {
  font-family: 'Courier New', monospace;
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.8rem;
  color: #495057;
  min-width: 80px;
}

.suggestion-desc {
  font-size: 0.85rem;
  color: #6c757d;
  flex: 1;
}

.live-preview-panel {
  margin-top: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  border-radius: 6px;
  border-left: 4px solid #17a2b8;
  max-height: 300px;
  overflow-y: auto;
}

.live-preview-panel h4 {
  margin: 0 0 0.75rem 0;
  color: #0c5460;
  font-size: 1rem;
}

.live-preview-content {
  background: rgba(255, 255, 255, 0.8);
  padding: 0.75rem;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.live-preview-content pre {
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.5;
  color: #0c5460;
  white-space: pre-wrap;
}

/* Enhanced editor styling */
.yaml-editor .ace_editor {
  border-radius: 6px;
  border: 2px solid #e9ecef;
  transition: border-color 0.2s ease;
}

.yaml-editor .ace_editor:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Auto-completion styling */
.ace_autocomplete {
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.ace_autocomplete .ace_marker-layer .ace_active-line {
  background: rgba(102, 126, 234, 0.1) !important;
}

.ace_autocomplete .ace_line-hover {
  background: rgba(102, 126, 234, 0.05) !important;
}

/* Responsive design */
@media (max-width: 1024px) {
  .converter-section,
  .editor-section {
    grid-template-columns: 1fr;
  }

  .app-main {
    padding: 1rem;
  }

  .app-header {
    padding: 1.5rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .live-preview-panel {
    max-height: 200px;
  }

  .suggestions-panel {
    padding: 0.75rem;
  }
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .button-group {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .btn {
    flex: 1;
    min-width: 120px;
  }

  .suggestion-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .adaptive-controls {
    padding: 0.5rem;
  }
}
